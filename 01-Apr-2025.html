<!DOCTYPE html>
<!-- saved from url=(0129)https://erp.sangamdairy.com/ReportTemplates/MarketingCentralizedInvoice.html?RefType=1&InvoiceIDList=NDU1NjQ2Ng==&&VehicleNo=LS0= -->
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <link rel="stylesheet" href="./01-Apr-2025_files/jquery-ui.css">

    <script src="./01-Apr-2025_files/jquery-1.12.4.js.download"></script>
    <script src="./01-Apr-2025_files/jquery-ui.js.download"></script>
    <link rel="stylesheet" href="./01-Apr-2025_files/bootstrap.min.css">
    <script src="./01-Apr-2025_files/jquery.min.js.download"></script>
    <script src="./01-Apr-2025_files/bootstrap.min.js.download"></script>
    <script src="./01-Apr-2025_files/jquery.min(1).js.download"></script>
    <link href="./01-Apr-2025_files/MarketingPrints.css" rel="stylesheet">
    <script src="./01-Apr-2025_files/jspdf.debug.js.download"></script>

    <style type="text/css">
        @media print {
            img {
                -webkit-print-color-adjust: exact;
            }

            body {
                background-color: #FFFFFF;
                background-image: none;
                color: #000000;
            }

            #ad {
                display: none;
            }

            #leftbar {
                display: none;
            }

            #contentarea {
                width: 100%;
            }
        }


        #myBtn {
            display: none;
            position: fixed;
            bottom: 20px;
            right: 30px;
            z-index: 99;
            font-size: 15px;
            border: none;
            outline: none;
            background-color: darkcyan;
            color: white;
            cursor: pointer;
            padding: 7px;
            border-radius: 4px;
        }

        #myBtn:hover {
            background-color: #555;
        }
    </style>
    <style>
        #Div_Invoice {
            width: 100%;
            min-height: 29.7cm;
            padding: 1cm;
            margin: 1cm auto;
            border: 1px #D3D3D3 solid;
            border-radius: 5px;
            background: white;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        }

        @page {
            size: A4;
            margin: 0;
        }

        @media print {
            #Div_Invoice {
                margin: 0;
                border: initial;
                border-radius: initial;
                width: initial;
                min-height: initial;
                box-shadow: initial;
                background: initial;
                page-break-after: always;
            }
        }
    </style>
</head>

<body>
    <div id="MyLoader" class="modal" data-backdrop="false" align="center" style="display: none;">
        <!-- Modal content -->
        <div class="modal-content"
            style="height:auto;overflow:auto;border-radius:0;margin-top:8%;width:30%;border:0px solid #eb0021">
            <div class="modal-body" style="padding:0;height:auto!important;" data-keyboard="false"
                data-backdrop="static">
                <div class="col-sm-12" style="border:2px solid #eb0021;padding:10px;border-color:forestgreen">
                    <div class="col-sm-12" style="font-weight:bold;font-size:13px;text-align:center"> Please Wait while
                        Loading Data.</div>
                    <div class="col-sm-12" style="text-align:center"><img id="imgurl" style="width:90%"
                            src="./01-Apr-2025_files/loadingImage.gif"></div>
                </div>
            </div>
        </div>
    </div>
    <button onclick="topFunction()" id="myBtn" title="Go to top" style="display: none;">Top ↑</button>
    <div class="col-sm-12" align="right">
        <div class="col-sm-11"></div>
        <div class="col-sm-12">
            <input type="button" class="btn btn-sm btn-success" value="Print" onclick="PrintDIV()">
        </div>

    </div>

    <div class="col-sm-6 clsInvoiceBody" id="Div_Invoice" align="center" style="padding:0;margin:0;">
        <div style="width:100%;margin-top:0%;page-break-before: always;" align="center" id="PrintDiv">
            <table width="100%" class="table table-bordered" cellspacing="0" cellpadding="0" style="">
                <tbody>
                    <tr>
                        <td align="center" valign="top" style="background-color:#FFFFFF;border:none!important"
                            bgcolor="#FF5D26;">
                            <table width="100%" class="table table-bordered" cellspacing="0" cellpadding="0">
                                <tbody>
                                    <tr>
                                        <td valign="top" bgcolor="#FFFFFF"
                                            style="border:none!important;background-color:#FFFFFF; color:#7b7b7b; font-family:Arial; font-size:14px;&gt;&lt;div style="
                                            font-size:16px;="" font-family:arial;'="">
                                            <div style="float:center;text-align:center;position:relative;"><span
                                                    style="float:left;margin-top:3%"></span><span
                                                    style="padding-left:0px;margin-top:5%;font-size:larger;font-family:Arial;color:black;font-style:normal"><br>SANGAM
                                                    MILK PRODUCER COMPANY LIMITED<br><span
                                                        style="padding-left:0px;font-size:16px;">Sangam
                                                        Dairy,Vadlamudi,Chebrolu Mandal,</span><br><span
                                                        style="padding-left:0px;font-size:16px;">Guntur District -
                                                        522213, A.P, GST NO :
                                                        37**********1ZC</span><br><span></span></span><span
                                                    style="padding-left:0px; margin-top:5%;font-size:16px;font-family:Arial;color:black;font-style:normal">PAN:**********</span><br><span
                                                    style="margin-top:5%;font-size:16px;font-family:Arial;color:black;font-style:normal">TAX
                                                    INVOICE</span><br><span
                                                    style="margin-top:5%;font-size:16px;font-family:Arial;color:black;font-style:normal">(Subject
                                                    to Andhra Pradesh Jurisdiction)</span><br>

                                                <div
                                                    style="position:absolute;top:1%;left:1%;display:block !important;text-align: -webkit-center;">
                                                    Scan QR for payment<img id="barcode"
                                                        src="./01-Apr-2025_files/InvoiceQR2.png" alt=""
                                                        title="VD951/2526" width="150" height="150"
                                                        style="display:block !important;"></div>
                                            </div>
                                            <div class="col-sm-12" style="padding-top:0.95vh"></div>
                                            <div class="col-sm-12">&nbsp;</div>
                                            <div class="div_MainClass">
                                                <table width="100%" class="table table-bordered">
                                                    <tbody>
                                                        <tr style="padding-top:10px;font-weight:bold;">
                                                            <td width="15%" valign="top" style="text-align:left;">Party
                                                                Code/Booth No &nbsp;:&nbsp;<span class="td_headLabel"
                                                                    id="SpnPartyID">8966/0</span> <br>Party Name
                                                                &nbsp;:&nbsp;<span class="td_headLabel"
                                                                    id="SpnPartyName">KANIGIRI BASAVESWARI,
                                                                    TADIKONDA</span> <br>Billing To:<span
                                                                    class="td_headLabel" id="SpnBillingto">Tadikonda,
                                                                    Tadikonda,
                                                                    ANDHRA PRADESH,
                                                                    GUNTUR,
                                                                    522236.</span>
                                                                <br>Phone&nbsp;&nbsp;&nbsp;&nbsp;:&nbsp;<span
                                                                    class="td_headLabel"
                                                                    id="Spnphone">8790611669</span><br>GSTIN
                                                                &nbsp;&nbsp;&nbsp;&nbsp;:&nbsp;<span
                                                                    class="td_headLabel" id="SpnGSTIN"></span><br>PAN
                                                                &nbsp;&nbsp;&nbsp;&nbsp;:&nbsp;<span
                                                                    class="td_headLabel"
                                                                    id="SpnPAN">**********</span><br>
                                                            </td>
                                                            <td width="15%" valign="top" style="text-align:left;">Party
                                                                Name&nbsp;&nbsp;:&nbsp;<span class="td_headLabel"
                                                                    id="SpnPartyname">KANIGIRI BASAVESWARI,
                                                                    TADIKONDA&nbsp;(8966)</span><br>Delivery
                                                                To&nbsp;&nbsp;:&nbsp;<span class="td_headLabel"
                                                                    id="SpnDeliveryto">Tadikonda,
                                                                    Tadikonda,
                                                                    ANDHRA PRADESH,
                                                                    GUNTUR,
                                                                    522236.</span><br>Phone
                                                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:&nbsp;<span
                                                                    class="td_headLabel"
                                                                    id="Spnphone1">8790611669</span><br>GSTIN
                                                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:&nbsp;<span
                                                                    class="td_headLabel" id="SpnGSTIN1"> </span><br>
                                                            </td>
                                                            <td width="15%" valign="top" style="text-align:left;"><span
                                                                    class="td_headLabel"> Invoice No</span><br><span
                                                                    class="td_headLabel">Sales/Service
                                                                    Order</span><br><span class="td_headLabel"> PO
                                                                    No</span><br><span class="td_headLabel"> Vehicle No
                                                                </span><br><span class="td_headLabel">Advance Doc. No
                                                                </span><br></td>
                                                            <td width="1%" valign="top" style="text-align:left;"><span
                                                                    class="td_headLabel">:</span><span
                                                                    class="td_FieldLabel"
                                                                    id="SpnInvoiceNo">VD951/2526</span><br><span
                                                                    class="td_headLabel">:&nbsp;</span><span
                                                                    class="td_FieldLabel"
                                                                    id="Spnsaleorserviceorder">---</span><br><span
                                                                    class="td_headLabel">:&nbsp;</span><span
                                                                    class="td_FieldLabel"
                                                                    id="SpnPono">---</span><br><span
                                                                    class="td_headLabel">:&nbsp;</span><span
                                                                    class="td_FieldLabel"
                                                                    id="Spnvehicleno">AP39UK5425</span><br><span
                                                                    class="td_headLabel">:&nbsp;</span><span
                                                                    class="td_FieldLabel"
                                                                    id="SpnAdvancedocno"></span><br></td>
                                                            <td width="15%" valign="top" style="text-align:left;"><span
                                                                    class="td_headLabel">Invoice Generated
                                                                    Date</span><br><span class="td_headLabel">Invoice
                                                                    Generated Time</span><br><span
                                                                    class="td_headLabel">Po Date/Delivery
                                                                    Date</span><br><span class="td_headLabel">
                                                                    Transporter Name </span><br><span
                                                                    class="td_headLabel">LR NO/Date</span><br></td>
                                                            <td width="10%" valign="top" style="text-align:left;"><span
                                                                    class="td_headLabel">:&nbsp;</span><span
                                                                    class="td_FieldLabel"
                                                                    id="SpnDate">01/04/2025</span><br><span
                                                                    class="td_headLabel">:&nbsp;</span><span
                                                                    class="td_FieldLabel"
                                                                    id="SpnTime">15:38:24</span><br><span
                                                                    class="td_headLabel">:&nbsp;</span><span
                                                                    class="td_FieldLabel"
                                                                    id="SpnpoDate">02/04/2025</span><br><span
                                                                    class="td_headLabel">:&nbsp;</span><span
                                                                    class="td_FieldLabel"
                                                                    id="SpnTransporterName">J.Madhubabu</span><br><span
                                                                    class="td_headLabel">:&nbsp;</span><span
                                                                    class="td_FieldLabel" id="Spnlrnodate"></span><br>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                                <div id="divtaxinvoice"
                                                    style="padding-right:20px;margin-left:-100px;width:180px"> </div>
                                            </div>
                                            <table class="tftable table table-bordered"
                                                style="border: 1px solid black;border-collapse: collapse;" id="Row_Id"
                                                width="100%">
                                                <tbody>
                                                    <tr style="border: 1px solid black;border-collapse: collapse;">
                                                        <th
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            SLNO</th>
                                                        <th
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            Item Code</th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                            Item Description</th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                            HSN/SAC Code</th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                            Quantity</th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                            Package Type</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            Rate(INR)</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            Net(INR)</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            Discount Amount</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            Taxable Amount</th>
                                                        <th colspan="2"
                                                            style="text-align: center;border: 1px solid black;border-collapse: collapse;">
                                                            SGST</th>
                                                        <th colspan="2"
                                                            style="text-align: center;border: 1px solid black;border-collapse: collapse;">
                                                            CGST</th>
                                                        <th colspan="2"
                                                            style="text-align: center;border: 1px solid black;border-collapse: collapse;">
                                                            IGST</th>
                                                        <th colspan="2"
                                                            style="text-align: center;border: 1px solid black;border-collapse: collapse;">
                                                            UTGST</th>
                                                        <th colspan="2"
                                                            style="text-align: center;border: 1px solid black;border-collapse: collapse;">
                                                            CESS</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            Total Amount(INR)</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            Remarks</th>
                                                    </tr>
                                                    <tr
                                                        style="border: 1px solid black;border: 1px solid black;border-collapse: collapse;">
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                        </th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                        </th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                        </th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                        </th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                        </th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                        </th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                        </th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                        </th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                        </th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                        </th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            Amt</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            %</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            Amt</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            %</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            Amt</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            %</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            Amt</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            %</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            Amt</th>
                                                        <th
                                                            style="text-align: right;border: 1px solid black;border-collapse: collapse;">
                                                            %</th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                        </th>
                                                        <th style="border: 1px solid black;border-collapse: collapse;">
                                                        </th>
                                                    </tr>
                                                    <tr style="border: 1px solid black;border-collapse: collapse;"></tr>
                                                    <tr style="border: 1px solid black;">
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="S_No">1</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Product_name">9</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Tubs">TM 500 ML</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Packets">04012000</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Qty">120</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="uom"> Packet </span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Rate">27.10</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="NetAmount">3252.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Disc_Amount">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Taxableamt">3252.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGST%">0</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">0</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESSamt">0.00</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESS%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">3252.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="ttamt">--</span>
                                                        </td>
                                                    </tr>
                                                    <tr style="border: 1px solid black;">
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="S_No">2</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Product_name">18</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Tubs">WHOLE MILK 500 ML</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Packets">04012000</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Qty">200</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="uom"> Packet </span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Rate">33.81</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="NetAmount">6762.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Disc_Amount">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Taxableamt">6762.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGST%">0</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">0</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESSamt">0.00</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESS%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">6762.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="ttamt">--</span>
                                                        </td>
                                                    </tr>
                                                    <tr style="border: 1px solid black;">
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="S_No">3</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Product_name">30</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Tubs">STM TEA 500 ML</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Packets">04012000</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Qty">200</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="uom"> Packet </span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Rate">32.83</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="NetAmount">6566.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Disc_Amount">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Taxableamt">6566.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGST%">0</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">0</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESSamt">0.00</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESS%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">6566.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="ttamt">--</span>
                                                        </td>
                                                    </tr>
                                                    <tr style="border: 1px solid black;">
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="S_No">4</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Product_name">277</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Tubs">TM 170 ML</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Packets">04012000</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Qty">500</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="uom"> Packet </span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Rate">9.06</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="NetAmount">4530.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Disc_Amount">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Taxableamt">4530.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGST%">0</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">0</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESSamt">0.00</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESS%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">4530.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="ttamt">--</span>
                                                        </td>
                                                    </tr>
                                                    <tr style="border: 1px solid black;">
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="S_No">5</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Product_name">34</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Tubs">CURD 450 GMS SACHET</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Packets">04039010</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Qty">120</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="uom"> Packet </span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Rate">28.38</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="NetAmount">3405.60</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Disc_Amount">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Taxableamt">3405.60</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGST%">51.09</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGST%">51.09</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGST%">0</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">0</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESSamt">0.00</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESS%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">3575.78</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="ttamt">--</span>
                                                        </td>
                                                    </tr>
                                                    <tr style="border: 1px solid black;">
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="S_No">6</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Product_name">37</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Tubs">CURD 500 G CUP</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Packets">04039010</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Qty">20</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="uom"> Cup </span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Rate">38.57</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="NetAmount">771.40</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Disc_Amount">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Taxableamt">771.40</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGST%">11.57</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGST%">11.57</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGST%">0</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">0</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESSamt">0.00</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESS%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">810.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="ttamt">--</span>
                                                        </td>
                                                    </tr>
                                                    <tr style="border: 1px solid black;">
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="S_No">7</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Product_name">235</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Tubs">CURD 75 GMS SACHET</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Packets">04039010</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Qty">150</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="uom"> Packet </span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Rate">4.76</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="NetAmount">714.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Disc_Amount">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Taxableamt">714.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGST%">11.90</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGST%">11.90</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGST%">0</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">0</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESSamt">0.00</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESS%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">750.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="ttamt">--</span>
                                                        </td>
                                                    </tr>
                                                    <tr style="border: 1px solid black;">
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="S_No">8</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Product_name">237</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Tubs">BUTTER MILK 200 ML SACHET</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Packets">04039010</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Qty">200</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="uom"> Packet </span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Rate">7.05</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="NetAmount">1410.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Disc_Amount">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Taxableamt">1410.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGST%">21.14</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGST%">21.14</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGST%">0</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">0</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESSamt">0.00</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESS%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">1480.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="ttamt">--</span>
                                                        </td>
                                                    </tr>
                                                    <tr style="border: 1px solid black;">
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="S_No">9</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Product_name">276</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Tubs">CURD 130 GMS SACHET</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Packets">04039010</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Qty">250</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="uom"> Packet </span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Rate">8.58</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="NetAmount">2145.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Disc_Amount">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Taxableamt">2145.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGST%">36.04</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGST%">36.04</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGST%">0</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">0</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESSamt">0.00</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESS%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">2253.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="ttamt">--</span>
                                                        </td>
                                                    </tr>
                                                    <tr style="border: 1px solid black;">
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="S_No">10</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Product_name">269</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Tubs">SABJA LASSI200ML</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Packets">04039010</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Qty">40</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="uom"> Dispo Bottele </span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Rate">16.19</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="NetAmount">647.60</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Disc_Amount">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Taxableamt">647.60</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGST%">9.71</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGST%">9.71</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGST%">0</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">0</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESSamt">0.00</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESS%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">680.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="ttamt">--</span>
                                                        </td>
                                                    </tr>
                                                    <tr style="border: 1px solid black;">
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="S_No">11</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Product_name">44</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Tubs">LASSI 200 ML GLASS</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Packets">04039010</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Qty">40</span>
                                                        </td>
                                                        <td
                                                            style="text-align:left;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="uom"> Glass </span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Rate">15.95</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="NetAmount">638.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Disc_Amount">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="Taxableamt">382.86</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGST%">9.57</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="SGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGST%">9.57</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CGSTamt">2.50</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGST%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="IGSTamt">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGST%">0</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">0</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESSamt">0.00</span>
                                                        </td>
                                                        <td class=" cess_hid"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="CESS%">0.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="UTGSTamt%">402.00</span>
                                                        </td>
                                                        <td
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            <span id="ttamt">--</span>
                                                        </td>
                                                    </tr>
                                                    <tr style="border: 1px solid black;border-collapse: collapse;">
                                                        <td colspan="4"
                                                            style="border: 1px solid black;border-collapse: collapse;">
                                                            Total</td>
                                                        <td colspan="1"
                                                            style="text-align:center;padding-left:0px;border: 1px solid black;border-collapse: collapse;">
                                                            1132</td>
                                                        <td colspan="2"
                                                            style="border: 1px solid black;border-collapse: collapse;">
                                                        </td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            18707.22</td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            0.00</td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            18707.22</td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            151.03</td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                        </td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            151.03</td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                        </td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            0.00</td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                        </td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            0</td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                        </td>
                                                        <td class=" cess_hid" colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            0.00</td>
                                                        <td class=" cess_hid" colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                        </td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                            19009.28</td>
                                                        <td colspan="1"
                                                            style="text-align:right;border: 1px solid black;border-collapse: collapse;">
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table><span style="float:right;"><br><br>Authorized Signatory<span
                                                    id="Spanauthorsig"></span></span>
                                            <table class="sizfnt table table-bordered" width="100%"
                                                style="color:black;font-family: " inder",=""
                                                sans-serif;margin-left:-2px"="">
                                                <tbody>
                                                    <tr>
                                                        <td style="text-align:left;"><span>Amount in Words: <span
                                                                    id="SpnAmountInWordsV">Nineteen Thousand Nine
                                                                </span></span></td>
                                                    </tr>
                                                    <tr>
                                                        <td style="text-align:left;"><span>Warranty:We hereby certify
                                                                that goods mentioned herein are warranted to be the same
                                                                in nature and a quality which these purport to be.Our
                                                                responsibility ceases upon delivery from our godown and
                                                                no claim for </span><span>loss,shortage or damage will
                                                                be entertained thereafter.</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td style="text-align:left;"><span>M/s Sangam Milk Producer
                                                                Company Limited. Tel No:0863-2025500,2027700 Email
                                                                Id:<EMAIL>&amp;<EMAIL>.</span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td style="text-align:left;"><span><br><br>Customers Seal and
                                                                Signature<span id="Spansealsig"></span></span><span
                                                                style="float:right;">for Sangam Milk Producer Company
                                                                Limited.,</span></td>
                                                    </tr>
                                                    <tr></tr>
                                                    <tr>
                                                        <td></td>
                                                    </tr>
                                                    <tr>
                                                        <td><span style="float:left;">Bank Details:</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><span style="float:left;">CMO TENALI : Bank Name: AXIS BANK
                                                                LTD.,A/C NO:***************,BRANCH:TENALI,IFSC
                                                                CODE:UTIB0000556,TYPE OF A/C:CURRENT ACCOUNT</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <div>&nbsp;</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div><b>"This is a system-generated invoice and does not require a signature/Stamp."</b><br><br><br>
    </div>


    <script type="text/javascript">
        var InvoiceIDList = ""; RefType = ""; var Taxableamount = 0; netamt = 0; qty = 0; Total_CGST = 0; Total_SGST = 0; Total_IGST = ""; Cgst_Total = ""; Sgst_Total = ""; Igst_Total = ""; Total_AmountBefore = ""; Total_UGST = 0; UGST_ = 0; CGST_ = 0; SGST_ = 0; IGST_ = 0; Grand_TotalAmount = 0; VehicleNo = ""; Total_Cess = 0; CESS_ = 0;

        $(document).ready(function () {
            debugger;

            var searchParams = new URLSearchParams(window.location.search);
            RefType = searchParams.get("RefType");
            VehicleNo = searchParams.get("VehicleNo");
            if (RefType == 1) {
                InvoiceIDList = atob(searchParams.get("InvoiceIDList"));
            }
            $("#ISRouteOrder").attr('checked', false);
            var Status = false;
            GetInvoiceList(RefType, InvoiceIDList, Status);
        })



        $("#ISRouteOrder").change(function () {
            if ($('#ISRouteOrder').prop('checked') == true) {
                var Status = true;
                GetInvoiceList(RefType, InvoiceIDList, Status);
            }
            else {
                var Status = false;
                GetInvoiceList(RefType, InvoiceIDList, Status);
            }

        })
        var CenterIndex = 0; var FinalListCount = 0;
        function GetInvoiceList(RefType, InvoiceIDList, Status) {
            $.ajax({
                url: "/Marketing/DispatchIndentReport/GetInvoicesOrder",
                data: { InvoiceIDString: InvoiceIDList },
                type: "GET",
                datatype: "JSON",
                success: function (data) {
                    var FinalList = data.split(',');
                    FinalListCount = FinalList.length;
                    for (var i = 0; i < FinalList.length; i++) {
                        GetInvoiceData(FinalList[i], Status);
                    }
                }
            })
        }
        function GetInvoiceData(DispatchID, Status) {
            $.ajax({
                url: "/Marketing/DispatchIndentReport/GetCentralDCGeneratedList",
                data: { id: DispatchID, RouteOrder: Status },
                type: "GET",
                datatype: "JSON",
                success: function (data) {
                    if (data.length > 0) {
                        LoadProductsDataToBind(data);
                    }
                }
            });
        }



        var Sno = 0; var IsAllowedToPrint = false; D_ID = 0;
        function LoadProductsDataToBind(data) {
            IsAllowedToPrint = false;
            var ID = data[0].InvoiceNo;
            D_ID = ID[0];
            var GSTPercentage = 0;
            var totalamount = 0.00; Total_discountamt = 0; Grand_TotalAmount = 0; netamt = 0; qty = 0; Taxableamount = 0; Total_CGST = 0; Total_SGST = 0; Total_IGST = ""; Total_Cess = "";
            var totalGST = 0.00;
            var StringHTML = "";

            StringHTML = StringHTML + "Party Code/Booth No &nbsp;:&nbsp;<span class='td_headLabel' id='SpnPartyID'>" + data[0].PartyId + "/" + data[0].BoothNo + "</span> <br />";
            StringHTML = StringHTML + "Party Name &nbsp;:&nbsp;<span class='td_headLabel' id='SpnPartyName'>" + data[0].PartyName + "</span> <br />";
            StringHTML = StringHTML + "Billing To:<span class='td_headLabel' id='SpnBillingto'>" + data[0].PartyBusinessAddres + "</span> <br />";
            if (data[0].PhoneNo == null) {
                data[0].PhoneNo = "";
            }
            StringHTML = StringHTML + 'Phone&nbsp;&nbsp;&nbsp;&nbsp;:&nbsp;<span class="td_headLabel"  id="Spnphone">' + data[0].PhoneNo + '</span><br />';
            if (data[0].GSTNNO == null) {
                data[0].GSTNNO = "";
            }
            StringHTML = StringHTML + 'GSTIN &nbsp;&nbsp;&nbsp;&nbsp;:&nbsp;<span class="td_headLabel" id="SpnGSTIN">' + data[0].GSTNNO + '</span><br />';
            if (data[0].PAN == null) {
                data[0].PAN = "";
            }
            StringHTML = StringHTML + 'PAN &nbsp;&nbsp;&nbsp;&nbsp;:&nbsp;<span class="td_headLabel" id="SpnPAN">' + data[0].PAN + '</span><br />';


            StringHTML = StringHTML + '</td>';
            StringHTML = StringHTML + '<td width="15%" valign="top" style="text-align:left;">';
            StringHTML = StringHTML + 'Party Name&nbsp;&nbsp;:&nbsp;<span class="td_headLabel" id="SpnPartyname">' + data[0].PartyName + '&nbsp;(' + data[0].PartyId + ')</span><br />';
            StringHTML = StringHTML + 'Delivery To&nbsp;&nbsp;:&nbsp;<span class="td_headLabel" id="SpnDeliveryto">' + data[0].PartyDeliveryAddres + '</span><br />';
            if (data[0].ExtraSMSPhoneNo == null) {
                data[0].ExtraSMSPhoneNo = "";
            }
            StringHTML = StringHTML + 'Phone &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:&nbsp;<span class="td_headLabel" id="Spnphone1">' + data[0].PhoneNo + '</span><br />';
            StringHTML = StringHTML + 'GSTIN &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:&nbsp;<span class="td_headLabel" id="SpnGSTIN1"> </span><br />';
            StringHTML = StringHTML + '</td>';
            StringHTML = StringHTML + '<td width="15%" valign="top" style="text-align:left;">';
            StringHTML = StringHTML + '<span class="td_headLabel"> Invoice No</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel">Sales/Service Order</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel"> PO No</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel"> Vehicle No  </span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel">Advance Doc. No   </span><br />';
            StringHTML = StringHTML + '</td>';
            StringHTML = StringHTML + '<td width="1%" valign="top" style="text-align:left;">';
            StringHTML = StringHTML + '<span class="td_headLabel">:</span><span class="td_FieldLabel" id="SpnInvoiceNo">' + data[0].InvoiceNo + '</span><br />';
            if (data[0].PO_NO == null) {
                data[0].PO_NO = "";
            }
            StringHTML = StringHTML + '<span class="td_headLabel">:&nbsp;</span><span class="td_FieldLabel" id="Spnsaleorserviceorder">' + (data[0].PONumber == null ? "---" : data[0].PONumber) + '</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel">:&nbsp;</span><span class="td_FieldLabel" id="SpnPono">' + (data[0].PONumber == null ? "---" : data[0].PONumber) + '</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel">:&nbsp;</span><span class="td_FieldLabel" id="Spnvehicleno">' + (data[0].VehicleNo) + '</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel">:&nbsp;</span><span class="td_FieldLabel" id="SpnAdvancedocno"></span><br />';
            StringHTML = StringHTML + '</td>';
            StringHTML = StringHTML + '<td width="15%" valign="top" style="text-align:left;">';
            StringHTML = StringHTML + '<span class="td_headLabel">Invoice Generated Date</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel">Invoice Generated Time</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel">Po Date/Delivery Date</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel"> Transporter Name </span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel">LR NO/Date</span><br />';
            StringHTML = StringHTML + '</td>';
            StringHTML = StringHTML + '<td width="10%" valign="top" style="text-align:left;">';
            StringHTML = StringHTML + '<span class="td_headLabel">:&nbsp;</span><span class="td_FieldLabel" id="SpnDate">' + data[0].DelivoryDate + '</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel">:&nbsp;</span><span class="td_FieldLabel" id="SpnTime">' + data[0].DelivoryTime + '</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel">:&nbsp;</span><span class="td_FieldLabel" id="SpnpoDate">' + ToJavaScriptDate(data[0].Date) + '</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel">:&nbsp;</span><span class="td_FieldLabel" id="SpnTransporterName">' + data[0].TranspoterName + '</span><br />';
            StringHTML = StringHTML + '<span class="td_headLabel">:&nbsp;</span><span class="td_FieldLabel" id="Spnlrnodate"></span><br />';
            StringHTML = StringHTML + '</td>';
            StringHTML = StringHTML + '</tr>';
            StringHTML = StringHTML + '</table>';
            StringHTML = StringHTML + '<div id="divtaxinvoice" style="padding-right:20px;margin-left:-100px;width:180px"> </div>';
            StringHTML = StringHTML + '</div>';

            StringHTML = StringHTML + ' <table class="tftable table table-bordered" style="border: 1px solid black;border-collapse: collapse;" id="Row_Id" width="100%" style="margin:0%;">';
            StringHTML = StringHTML + ' <tr style="border: 1px solid black;border-collapse: collapse;">';
            StringHTML = StringHTML + '<th style="text-align:left;border: 1px solid black;border-collapse: collapse;">SLNO</th>';
            StringHTML = StringHTML + '<th style="text-align:left;border: 1px solid black;border-collapse: collapse;">Item Code</th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;">Item Description</th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;">HSN/SAC Code</th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;">Quantity</th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;">Package Type</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">Rate(INR)</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">Net(INR)</th>';

            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">Discount Amount</th>';

            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">Taxable Amount</th>';
            StringHTML = StringHTML + '<th colspan="2" style="text-align: center;border: 1px solid black;border-collapse: collapse;">SGST</th>';
            StringHTML = StringHTML + '<th colspan="2" style="text-align: center;border: 1px solid black;border-collapse: collapse;">CGST</th>';
            StringHTML = StringHTML + '<th colspan="2" style="text-align: center;border: 1px solid black;border-collapse: collapse;">IGST</th>';
            StringHTML = StringHTML + '<th colspan="2" style="text-align: center;border: 1px solid black;border-collapse: collapse;">UTGST</th>';
            StringHTML = StringHTML + '<th colspan="2" style="text-align: center;border: 1px solid black;border-collapse: collapse;">CESS</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">Total Amount(INR)</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">Remarks</th>';
            StringHTML = StringHTML + ' </tr>';
            StringHTML = StringHTML + '<tr style="border: 1px solid black;border: 1px solid black;border-collapse: collapse;">';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;"></th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;"></th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;"></th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;"></th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;"></th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;"></th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;"></th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;"></th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;"></th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;"></th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">Amt</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">%</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">Amt</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">%</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">Amt</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">%</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">Amt</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">%</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">Amt</th>';
            StringHTML = StringHTML + '<th style="text-align: right;border: 1px solid black;border-collapse: collapse;">%</th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;"></th>';
            StringHTML = StringHTML + '<th style="border: 1px solid black;border-collapse: collapse;"></th>';
            StringHTML = StringHTML + ' </tr> ';
            StringHTML = StringHTML + '<tr style="border: 1px solid black;border-collapse: collapse;"></tr>';
            var QRInvoiceNo = data[0].InvoiceNo;
            var QRInvoiceDate = ToJavaScriptDate(data[0].Date);

            var Cess_Amt = "";
            var Cess_Per = "";
            var Total_Cess_Amt = "";
            for (var i = 0; i < data.length; i++) {

                if (data[i].CESS_AMt == null) {
                    Cess_Amt = 0;
                } else {
                    Cess_Amt = data[i].CESS_AMt;
                }

                if (data[i].CESSPercentage == null) {
                    Cess_Per = 0;
                } else {
                    Cess_Per = data[i].CESSPercentage;
                }


                if (i == 0) {

                    Total_Cess_Amt = Cess_Amt;
                } else {
                    Total_Cess_Amt = Total_Cess_Amt + Cess_Amt;

                }

                var IndexValue = 0;
                //if (data[i].SGstAmount != 0 && data[i].CGstAmount != 0) {
                IsAllowedToPrint = true;
                IndexValue = IndexValue + 1;


                var ppid = data[i].ProductId;
                GSTPercentage = parseFloat(data[i].SGst) + parseFloat(data[i].CGst) + parseFloat(data[i].IGst) + parseFloat(Cess_Per);

                var Unitprice = parseFloat(data[i].UnitPrice) - ((parseFloat(data[i].UnitPrice) * GSTPercentage) / (100 + GSTPercentage));
                //var Unit_price = parseFloat(data[i].UnitPrice);
                var Unit_price = parseFloat(Unitprice);
                var amount = ""; DiscountAmount = "";

                DiscountAmount = parseFloat(data[i].FreeQty) * parseFloat(Unit_price);
                if (DiscountAmount != null) {
                    Total_discountamt = parseFloat(Total_discountamt.toFixed(2)) + parseFloat(DiscountAmount.toFixed(2));
                }
                else {
                    Total_discountamt = parseFloat(DiscountAmount);
                }
                var TotalQty = parseFloat(data[i].Quantity) + data[i].FreeQty
                if (data[i].Amount != null) {
                    amount = parseFloat(TotalQty) * parseFloat(Unit_price);
                }
                else {
                    amount = 0;
                }
                if (data[i].Quantity != null) {
                    qty = qty + TotalQty;
                }
                else { qty = 0; }
                if (amount != null) {
                    netamt = netamt + amount;
                }
                else {
                    netamt = 0
                }
                var Total_Taxamount = parseFloat(amount.toFixed(2)) - parseFloat(DiscountAmount.toFixed(2));
                var SGSTamount = 0; CGSTamount = 0; IGSTamount = 0; CESSamount = 0;
                if (data[i].SGstAmount != null) {
                    var SGSTamount = (parseFloat(Total_Taxamount * data[i].SGst) / 100)
                }
                else {
                    SGSTamount = 0;
                }
                if (data[i].CGstAmount != null) {
                    CGSTamount = (parseFloat(Total_Taxamount * data[i].CGst) / 100)
                }
                else {
                    CGSTamount = 0;
                }
                if (data[i].IGstAmount != null) {
                    IGSTamount = (parseFloat(Total_Taxamount * data[i].IGst) / 100)
                }
                else {
                    IGSTamount = 0;
                }


                if (data[i].CESS_AMt != null) {
                    var CESSamount = (parseFloat(Total_Taxamount * data[i].CESSPercentage) / 100)
                }
                else {
                    CESSamount = 0;
                }

                var Taxamount = SGSTamount + CGSTamount + IGSTamount;

                //------ SGST ---
                var Sgst = "";
                if (data[i].SGstAmount != null) {
                    SGST_ = (parseFloat(data[i].SGst * data[i].UnitPrice) / 100);
                    if (Total_SGST == "") {
                        Total_SGST = parseFloat(SGSTamount);
                    }
                    else {
                        Total_SGST = Total_SGST + parseFloat(SGSTamount);
                    }

                }
                else {
                    Total_SGST = 0;
                    SGST_ = 0;
                }

                //------ CGST ---
                var Cgst = "";
                if (data[i].CGstAmount != null) {
                    CGST_ = (parseFloat(data[i].CGst * data[i].UnitPrice) / 100);
                    if (Total_CGST == "") {
                        Total_CGST = parseFloat(CGSTamount);
                    }
                    else {
                        Total_CGST = parseFloat(Total_CGST) + parseFloat(CGSTamount);
                    }
                }
                else {
                    Total_CGST = 0;
                    CGST_ = 0;
                }

                //------ IGST ---
                var Igst = "";
                if (data[i].IGstAmount != null) {
                    IGST_ = (parseFloat(data[i].IGst * data[i].UnitPrice) / 100);
                    if (Total_IGST == "") {
                        Total_IGST = IGSTamount;
                    }
                    else {
                        Total_IGST = Total_IGST + parseFloat(IGSTamount);
                    }
                }
                else {
                    Total_IGST = 0;
                    IGST_ = 0;
                }

                //-----cess-----------

                var cess = "";
                if (data[i].CESS_AMt != null) {
                    CESS_ = (parseFloat(data[i].CESSPercentage * data[i].UnitPrice) / 100);
                    if (Total_Cess == "") {
                        Total_Cess = CESSamount;
                    }
                    else {
                        Total_Cess = Total_Cess + parseFloat(CESSamount);
                    }
                }
                else {
                    Total_Cess = 0;
                    CESS_ = 0;
                }
                var TotalAmount_WithTax = parseFloat(data[i].Amount);
                //---- End ------
                if (Total_Taxamount != null) {
                    Taxableamount = parseFloat(Taxableamount) + parseFloat(Total_Taxamount);
                }
                else {
                    Taxableamount = 0
                }
                //----- Grand Total ------
                var TotalAmount = "";
                if (TotalAmount == "") {
                    TotalAmount = parseFloat(amount);
                    Grand_TotalAmount = Grand_TotalAmount + TotalAmount_WithTax;
                }
                else {
                    Grand_TotalAmount = 0;
                }
                //var sno = i + 1;
                Sno++;
                QRTotalAmount = parseFloat(QRTotalAmount + TotalAmount_WithTax);
                QRCGST = (parseFloat(QRCGST) + parseFloat(CGSTamount)).toFixed(2);
                QRSGST = (parseFloat(QRSGST) + parseFloat(SGSTamount)).toFixed(2);
                QRIGST = (parseFloat(QRIGST) + parseFloat(IGSTamount)).toFixed(2);
                var GST = 0.00;
                GST = parseFloat(GST + data[i].GstAmount).toFixed(2);;
                var WithOutGST = 0.00; WithOutGST = parseFloat(WithOutGST) + parseFloat(data[i].WithoutGST)
                StringHTML = StringHTML + '<tr style="border: 1px solid black;">';
                StringHTML = StringHTML + '<td style="text-align:left;border: 1px solid black;border-collapse: collapse;"><span id="S_No">' + parseInt(i + 1) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:left;border: 1px solid black;border-collapse: collapse;"><span id="Product_name">' + data[i].ProductId + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:left;border: 1px solid black;border-collapse: collapse;"><span id="Tubs">' + data[i].ProductName + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:left;border: 1px solid black;border-collapse: collapse;"><span id="Packets">' + data[i].HSNCode + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:left;border: 1px solid black;border-collapse: collapse;"><span id="Qty">' + TotalQty + '</span></td>';
                StringHTML = StringHTML + '<td style="text-align:left;border: 1px solid black;border-collapse: collapse;"><span id="uom"> ' + data[i].PackingType_Name + ' </span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="Rate">' + Unit_price.toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="NetAmount">' + parseFloat(amount).toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="Disc_Amount">' + parseFloat(DiscountAmount).toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="Taxableamt">' + parseFloat(Total_Taxamount).toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="SGST%">' + parseFloat(SGSTamount).toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="SGSTamt">' + data[i].SGst.toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="CGST%">' + parseFloat(CGSTamount).toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="CGSTamt">' + data[i].CGst.toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="IGST%">' + parseFloat(IGSTamount).toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="IGSTamt">' + data[i].IGst.toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="UTGST%">' + 0 + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="UTGSTamt%">' + 0 + '</span></td>'

                StringHTML = StringHTML + '<td class=" cess_hid" style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="CESSamt">' + parseFloat(CESSamount).toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td  class=" cess_hid" style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="CESS%">' + parseFloat(Cess_Per).toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="UTGSTamt%">' + parseFloat(TotalAmount_WithTax).toFixed(2) + '</span></td>'
                StringHTML = StringHTML + '<td style="text-align:right;border: 1px solid black;border-collapse: collapse;"><span id="ttamt">' + '--' + '</span></td></tr>';
            }
            if (TotalAmount != null) {
                StringHTML = StringHTML + '<tr style="border: 1px solid black;border-collapse: collapse;">';
                StringHTML = StringHTML + '<td colspan="4" style="border: 1px solid black;border-collapse: collapse;">Total</td>';
                StringHTML = StringHTML + '<td colspan="1" style="text-align:center;padding-left:0px;border: 1px solid black;border-collapse: collapse;">' + qty + '</td>';
                StringHTML = StringHTML + '<td colspan="2" style="border: 1px solid black;border-collapse: collapse;"></td>';
                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;">' + netamt.toFixed(2) + '</td>';

                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;">' + parseFloat(Total_discountamt).toFixed(2) + '</td>';

                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;">' + parseFloat(Taxableamount).toFixed(2) + '</td>';
                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;">' + Total_SGST.toFixed(2) + '</td>';
                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;"></td>';
                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;">' + Total_CGST.toFixed(2) + '</td>';
                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;"></td>';
                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;">' + Total_IGST.toFixed(2) + '</td>';
                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;"></td>';
                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;">' + 0 + '</td>';
                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;"></td>';
                StringHTML = StringHTML + '<td class=" cess_hid" colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;">' + Total_Cess.toFixed(2) + '</td>';
                StringHTML = StringHTML + '<td  class=" cess_hid" colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;"></td>';

                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;">' + Grand_TotalAmount.toFixed(2) + '</td>';
                StringHTML = StringHTML + '<td colspan="1" style="text-align:right;border: 1px solid black;border-collapse: collapse;"></td>';
                StringHTML = StringHTML + '</tr>';
            }
            var QRTotalAmount = Grand_TotalAmount;
            var QRCGST = Total_CGST;
            var QRSGST = Total_SGST;
            var QRIGST = Total_IGST;
            if (data[0].TCSAmount != null && data[0].TCSAmount != undefined) {
                if (parseFloat(data[0].TCSAmount) != 0) {
                    StringHTML = StringHTML + '<td colspan="18" style="text-align:left;border: 1px solid black;border-collapse: collapse;">TCS Amount</td><td style="text-align:right;border: 1px solid black;border-collapse: collapse;">' + data[0].TCSAmount + '</td></tr>'
                    Grand_TotalAmount = parseFloat(Grand_TotalAmount) + parseFloat(data[0].TCSAmount);
                    StringHTML = StringHTML + '<td colspan="18" style="text-align:left;border: 1px solid black;border-collapse: collapse;">Total Amount</td><td style="text-align:right;border: 1px solid black;border-collapse: collapse;">' + parseFloat(Grand_TotalAmount).toFixed(2) + '</td></tr>'
                }
            }
            StringHTML = StringHTML + '</table>'
            StringHTML = StringHTML + '<table class="sizfnt table table-bordered" width="100%" style="color:black;font-family: "Inder", sans-serif;margin-left:-2px">'
            StringHTML = StringHTML + '<tr>'


            StringHTML = StringHTML + '<td style="text-align:left;">'
            StringHTML = StringHTML + '<span>Amount in Words: <span id="SpnAmountInWords' + D_ID + '">' + convertNumberToWords(Grand_TotalAmount) + '</span></span>'
            StringHTML = StringHTML + '</td></tr><tr>'
            StringHTML = StringHTML + '<td style="text-align:left;">'
            StringHTML = StringHTML + '<span>Warranty:We hereby certify that goods mentioned herein are warranted to be the same in nature and a quality which these purport to be.Our responsibility ceases upon delivery from our godown and no claim for </span>'
            StringHTML = StringHTML + '<span>loss,shortage or damage will be entertained thereafter.</span>'
            StringHTML = StringHTML + '</td></tr><tr>'
            StringHTML = StringHTML + '<td style="text-align:left;">'
            StringHTML = StringHTML + '<span>M/s Sangam Milk Producer Company Limited. Tel No:0863-2025500,2027700 Email Id:<EMAIL>&<EMAIL>.</span></td></tr><tr>'
            StringHTML = StringHTML + '<td style="text-align:left;">'
            StringHTML = StringHTML + '<span><br /><br />Customers Seal and Signature<span id="Spansealsig"></span></span>'
            StringHTML = StringHTML + '<span style="float:right;">for Sangam Milk Producer Company Limited.,</span></td></tr><tr></tr><tr><td>'
            StringHTML = StringHTML + '<tr><td><span style="float:left;">Bank Details:</span></td></tr>'
            if (data[0].MSOID == 9) {
                StringHTML = StringHTML + '<tr><td><span style="float:left;">CMO VIJAYAWADA : Bank Name: AXIS BANK LTD.,A/C NO:***************,BRANCH:VIJAYAWADA ONE TOWN,IFSC CODE:UTIB0000555,TYPE OF A/C:CURRENT ACCOUNT</span></td></tr>'
                StringHTML = StringHTML + '<tr><td><span style="float:left;">CMO VIJAYAWADA : Bank Name: UNION BANK,A/C NO:***************,BRANCH:KRISHNA LANKA,IFSC CODE:UBIN0803227,TYPE OF A/C:CURRENT ACCOUNT</span></td></tr>'
            }
            else if (data[0].MSOID == 27) {
                StringHTML = StringHTML + '<tr><td><span style="float:left;">CMO KHAMMAM : Bank Name: AXIS BANK,A/C NO:***************,BRANCH:ROTARY NAGAR,IFSC CODE:UTIB0000273,TYPE OF A/C:CURRENT ACCOUNT</span></td></tr>'
            }
            else if (data[0].MSOID == 12) {
                StringHTML = StringHTML + '<tr><td><span style="float:left;">CMO TENALI : Bank Name: AXIS BANK LTD.,A/C NO:***************,BRANCH:TENALI,IFSC CODE:UTIB0000556,TYPE OF A/C:CURRENT ACCOUNT</span></td></tr>'
            }
            else if (data[0].MSOID == 6) {
                StringHTML = StringHTML + '<tr><td><span style="float:left;">CMO GUNTUR : Bank Name:ANDHRA BANK.,A/C NO:***************,BRANCH:KORETIPADU,IFSC CODE:ANDB0000314,TYPE OF A/C:CURRENT ACCOUNT </span>'
            }
            else if (data[0].MSOID == 44 || data[0].MSOID == 45) {
                StringHTML = StringHTML + '<tr><td><span style="float:left;">CMO GUNTUR : Bank Name:UNION BANK OF INDIA.,A/C NO:***************,BRANCH:GUNTUR MARKET CENTER,IFSC CODE:UBIN0901504,TYPE OF A/C:CURRENT ACCOUNT </span>'
            }
            else {
                StringHTML = StringHTML + '<tr><td><span style="float:left;">CMO TENALI : Bank Name: AXIS BANK LTD.,A/C NO:***************,BRANCH:TENALI,IFSC CODE:UTIB0000556,TYPE OF A/C:CURRENT ACCOUNT</span></td></tr>'
            }
            //StringHTML = StringHTML + '<tr><td><span style="float:left;">CMO TIRUPATHI : Bank Name: AXIS BANK,A/C NO:***************,BRANCH:ROTARY NAGAR,IFSC CODE:UTIB0000273,TYPE OF A/C:CURRENT ACCOUNT</span></td></tr>'
            StringHTML = StringHTML + '<span style="float:right;"><br /><br />Authorized Signatory<span id="Spanauthorsig"></span></span></td></tr></table><div>&nbsp;</div></td></tr></table></td></tr></table></div><b>"This is a system-generated invoice and does not require a signature/Stamp."</b><br/><br/><br/>'
            var QRString = 'GSTIN+of+the+supplier+-+37**********1ZC+%0ASupplier+UPI+ID+-+sangam1.07%40cmsidfc+%0AInvoice+number+-+' + QRInvoiceNo + '+%0ADate+of+Invoice+-+' + QRInvoiceDate + '+%0ATotal+Invoice+Value+-+' + QRTotalAmount.toFixed(2) + '%0ABank+account+details+of+the+payee+and+IFSC+-+IDFC+First+Bank%2C+A%2Fc+No.+***********%2C+First+Floor%2C+BG+Arcade%2C+Guntur.+IFSC+Code+-+IDFB0080381+%0ACGST+-+' + QRCGST.toFixed(2) + '+%0ASGST+-+' + QRSGST.toFixed(2) + '+%0AIGST+-+' + QRIGST.toFixed(2) + '+%0Acess+-+0&qzone=0&margin=0&size=1000x1000&ecc=L'
            //var QRString = 'chl=GSTIN+of+the+supplier+37**********1ZC%0Asupplier+UPI+ID+-+sangam1.07%40cmsidfc%0AInvoice+Number+-+' + QRInvoiceNo + '%0ADate+of+invoice+-+' + QRInvoiceDate + '%0ATotal+Invoice+Value+-+' + QRTotalAmount.toFixed(2) + '%0ABank+details+of+payee+and+IFSC+-+IDFC+First+Bank+A%2Fc+No.+***********%2CFirst+Floor%2C+BG+Acrade%2C+Guntur.+IFSC+Code+-+IDFB0080381%0ACGST+-+' + QRCGST.toFixed(2) + '%0ASGST+-+' + QRSGST.toFixed(2) + '%0AIGST+-+' + QRIGST.toFixed(2) + '%0Acess+-+0';
            QRString = 'https://api.qrserver.com/v1/create-qr-code/?color=000000&bgcolor=FFFFFF&data=' + QRString;
            QRString = '<div style="position:absolute;top:1%;right:1%;display:block !important;text-align: -webkit-center;">Scan QR for invoice details<img id="barcode" src="' + QRString + '" alt="" title="' + QRInvoiceNo + '" width="150" height="150" style="display:block !important;"/></div>';
            QRString = QRString + '<div style="position:absolute;top:1%;left:1%;display:block !important;text-align: -webkit-center;">Scan QR for payment<img id="barcode" src="../Images/InvoiceQR2.png" alt="" title="' + QRInvoiceNo + '" width="150" height="150" style="display:block !important;"/></div>';
            var StringHTML1 = "";
            StringHTML1 = StringHTML1 + '<div style="width:100%;margin-top:0%;page-break-before: always;" align="center" id="PrintDiv">';
            StringHTML1 = StringHTML1 + '<table width="100%" class="table table-bordered" cellspacing="0" cellpadding="0" style="">';
            StringHTML1 = StringHTML1 + '<tr>';
            StringHTML1 = StringHTML1 + '<td align="center" valign="top" style="background-color:#FFFFFF;border:none!important" bgcolor="#FF5D26;">';
            StringHTML1 = StringHTML1 + '<table width="100%" class="table table-bordered" cellspacing="0" cellpadding="0">';
            StringHTML1 = StringHTML1 + '<tr>';
            StringHTML1 = StringHTML1 + "<td valign='top' bgcolor='#FFFFFF' style='border:none!important;background-color:#FFFFFF; color:#7b7b7b; font-family:Arial; font-size:14px;>";
            StringHTML1 = StringHTML1 + "<div style='font-size:16px; font-family:Arial;'>";
            StringHTML1 = StringHTML1 + "<div style='float:center;text-align:center;position:relative;'>";
            StringHTML1 = StringHTML1 + "<span style='float:left;margin-top:3%'></span>";
            StringHTML1 = StringHTML1 + "<span style='padding-left:0px;margin-top:5%;font-size:larger;font-family:Arial;color:black;font-style:normal'>";

            if (data[0].MSOID == 9 || data[0].MSOID == 27 || data[0].MSOID == 31 || data[0].MSOID == 32 || data[0].MSOID == 43) {
                StringHTML1 = StringHTML1 + '<span style="padding-left:0px;font-size:17px;font-weight:bold;">' + data[0].MSO + '</span>';
                StringHTML1 = StringHTML1 + "<br />";
                StringHTML1 = StringHTML1 + '<span style="padding-left:0px;font-size:16px;">A Unit Of</span>';
            }
            StringHTML1 = StringHTML1 + "<br />";
            StringHTML1 = StringHTML1 + "SANGAM MILK PRODUCER COMPANY LIMITED";
            StringHTML1 = StringHTML1 + "<br />";
            if (data[0].DispatchDairy == "33") {
                StringHTML1 = StringHTML1 + '<span style="padding-left:0px;font-size:16px;">Sangam Dairy,Vadlamudi,Chebrolu Mandal,</span>';
                StringHTML1 = StringHTML1 + "<br />";
                StringHTML1 = StringHTML1 + '<span style="padding-left:0px;font-size:16px;">Guntur District - 522213, A.P,  GST NO : 37**********1ZC</span>';
                StringHTML1 = StringHTML1 + '<br />';

            }
            else {
                StringHTML1 = StringHTML1 + '<span style="padding-left:0px;font-size:16px;">SANGAM MILK PRODUCER COMPANY LTD,6-298, SRINIVASA NAGAR - VILLAGE, TUNGAPAHAD</span>';
                StringHTML1 = StringHTML1 + "<br />";
                StringHTML1 = StringHTML1 + '<span style="padding-left:0px;font-size:16px;">MIRYALAGUDA - MANDAL, NALGONDA - DIST, PIN CODE - 508207, TELANGANA - STATE. GST NO : 36**********1ZE</span>';
                StringHTML1 = StringHTML1 + '<br />';

            }
            StringHTML1 = StringHTML1 + '<span></span></span>';
            StringHTML1 = StringHTML1 + "<span style='padding-left:0px; margin-top:5%;font-size:16px;font-family:Arial;color:black;font-style:normal'>PAN:**********</span><br />";
            //StringHTML1 = StringHTML1 + "<span style='padding-left:0px; margin-top:5%;font-size:16px;font-family:Arial;color:black;font-style:normal'>ISO 22000 : 2005 CERTIFIED DAIRY</span><br />";
            if (data[0].InvoiceCancelStatus == 1) {
                if (GSTPercentage > 0) {
                    StringHTML1 = StringHTML1 + "<span style='margin-top:5%;font-size:16px;font-family:Arial;color:black;font-style:normal;font-weight:bold;color:red;'>TAX INVOICE ( CANCELLED )</span><br />";
                }
                else {
                    StringHTML1 = StringHTML1 + "<span style='margin-top:5%;font-size:16px;font-family:Arial;color:black;font-style:normal;font-weight:bold;color:red;'>BILL OF SUPPLY ( CANCELLED )</span><br />";
                }

            }
            else {
                if (GSTPercentage > 0) {
                    StringHTML1 = StringHTML1 + "<span style='margin-top:5%;font-size:16px;font-family:Arial;color:black;font-style:normal'>TAX INVOICE</span><br />";
                }
                else {
                    StringHTML1 = StringHTML1 + "<span style='margin-top:5%;font-size:16px;font-family:Arial;color:black;font-style:normal'>BILL OF SUPPLY</span><br />";
                }

            }
            StringHTML1 = StringHTML1 + "<span style='margin-top:5%;font-size:16px;font-family:Arial;color:black;font-style:normal'>(Subject to Andhra Pradesh Jurisdiction)</span><br />";
            StringHTML1 = StringHTML1 + QRString + '</div>';
            StringHTML1 = StringHTML1 + '</div>';
            StringHTML1 = StringHTML1 + '<div class="col-sm-12" style="padding-top:0.95vh">';
            StringHTML1 = StringHTML1 + '</div>';
            StringHTML1 = StringHTML1 + '<div class="col-sm-12">&nbsp;</div>';
            StringHTML1 = StringHTML1 + '<div class="div_MainClass">';
            StringHTML1 = StringHTML1 + '<table width="100%" class="table table-bordered">';
            StringHTML1 = StringHTML1 + '<tr style="padding-top:10px;font-weight:bold;">';
            StringHTML1 = StringHTML1 + "<td width='15%' valign='top' style='text-align:left;'>";


            if (IsAllowedToPrint == true) {
                $("#Div_Invoice").append(StringHTML1 + StringHTML);
            }
        }
        function convertNumberToWords(amount) {
            var words = new Array();
            words[0] = ''; words[1] = 'One'; words[2] = 'Two'; words[3] = 'Three'; words[4] = 'Four'; words[5] = 'Five';
            words[6] = 'Six'; words[7] = 'Seven'; words[8] = 'Eight'; words[9] = 'Nine'; words[10] = 'Ten'; words[11] = 'Eleven';
            words[12] = 'Twelve'; words[13] = 'Thirteen'; words[14] = 'Fourteen'; words[15] = 'Fifteen'; words[16] = 'Sixteen';
            words[17] = 'Seventeen'; words[18] = 'Eighteen'; words[19] = 'Nineteen'; words[20] = 'Twenty'; words[30] = 'Thirty';
            words[40] = 'Forty'; words[50] = 'Fifty'; words[60] = 'Sixty'; words[70] = 'Seventy'; words[80] = 'Eighty'; words[90] = 'Ninety';
            amount = amount.toString();
            var atemp = amount.split(".");
            var number = atemp[0].split(",").join("");
            var n_length = number.length;
            var words_string = "";
            if (n_length <= 9) {
                var n_array = new Array(0, 0, 0, 0, 0, 0, 0, 0, 0);
                var received_n_array = new Array();
                for (var i = 0; i < n_length; i++) {
                    received_n_array[i] = number.substr(i, 1);
                }
                for (var i = 9 - n_length, j = 0; i < 9; i++, j++) {
                    n_array[i] = received_n_array[j];
                }
                for (var i = 0, j = 1; i < 9; i++, j++) {
                    if (i == 0 || i == 2 || i == 4 || i == 7) {
                        if (n_array[i] == 1) {
                            n_array[j] = 10 + parseInt(n_array[j]);
                            n_array[i] = 0;
                        }
                    }
                }
                value = "";
                for (var i = 0; i < 9; i++) {
                    if (i == 0 || i == 2 || i == 4 || i == 7) {
                        value = n_array[i] * 10;
                    } else {
                        value = n_array[i];
                    }
                    if (value != 0) {
                        words_string += words[value] + " ";
                    }
                    if ((i == 1 && value != 0) || (i == 0 && value != 0 && n_array[i + 1] == 0)) {
                        words_string += "Crores ";
                    }
                    if ((i == 3 && value != 0) || (i == 2 && value != 0 && n_array[i + 1] == 0)) {
                        words_string += "Lakhs ";
                    }
                    if ((i == 5 && value != 0) || (i == 4 && value != 0 && n_array[i + 1] == 0)) {
                        words_string += "Thousand ";
                    }
                    if (i == 6 && value != 0 && (n_array[i + 1] != 0 && n_array[i + 2] != 0)) {
                        words_string += "Hundred and ";
                    } else if (i == 6 && value != 0) {
                        words_string += "Hundred ";
                    }
                }
                words_string = words_string.split("  ").join(" ");
            }
            $('#spnamtwrds').html('<b>' + words_string + ' ' + 'Rupees only' + '</b >').show();
            return words_string;
        }
        function ToJavaScriptDate(value) {
            var pattern = /Date\(([^)]+)\)/;
            var results = pattern.exec(value);
            if (results != null) {
                var dt = new Date(parseFloat(results[1]));
                var d = (dt.getMonth() + 1).toString();
                if (d.length == 1) {
                    d = "0" + d;
                }
                var dd = (dt.getDate()).toString();
                if (dd.length == 1) {
                    dd = "0" + dd;
                }
                return dd + "/" + d + "/" + dt.getFullYear();
            } else {
                return value;
            }
        }
        function PrintDIV() {
            var divToPrint = document.getElementById('Div_Invoice');
            var htmlToPrint = '' +
                '<style type="text/css">' +
                'table { font-family:Calibri;display: block;font-size: auto;font-size:18px;width:100% }' +
                ' th {font-size:18px;font-weight:normal }' +
                ' td{font-size:18px;font-weight:normal } }' +
                '}' + ' ' + ' .EditPROItems{display:none} ' +
                '</style>';
            htmlToPrint += divToPrint.outerHTML;
            newWin = window.open("");
            newWin.document.write(htmlToPrint);
            setTimeout(function () { newWin.print(); newWin.close(); }, 1000);

            var divContents = $("#Div_Invoice").html();
        }
        var mybutton = document.getElementById("myBtn");
        // When the user scrolls down 20px from the top of the document, show the button
        window.onscroll = function () { scrollFunction() };

        function scrollFunction() {
            if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                mybutton.style.display = "block";
            } else {
                mybutton.style.display = "none";
            }
        }
        function BottomFunction() {
            document.body.scrollBottom = 0;
            document.documentElement.scrollBottom = 0;
        }
        // When the user clicks on the button, scroll to the top of the document
        function topFunction() {
            $('html,body').animate({ scrollTop: 0 }, 'slow');
            document.body.scrollTop = 0;
            document.documentElement.scrollTop = 0;
        }
        $(document).ajaxStart(function () {
            $('#MyLoader').show();
        });
        $(document).ajaxStop(function () {
            $('#MyLoader').hide();
        });
        document.onkeydown = function (e) {
            if (event.keyCode == 123) {
                return false;
            }
            if (e.ctrlKey && e.shiftKey && e.keyCode == 'I'.charCodeAt(0)) {
                return false;
            }
            if (e.ctrlKey && e.shiftKey && e.keyCode == 'C'.charCodeAt(0)) {
                return false;
            }
            if (e.ctrlKey && e.shiftKey && e.keyCode == 'J'.charCodeAt(0)) {
                return false;
            }
            if (e.ctrlKey && e.keyCode == 'U'.charCodeAt(0)) {
                return false;
            }
        }

    </script>
</body>

</html>